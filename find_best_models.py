"""
FIJI/ImageJ Machine Learning Pipeline - Complete Three-Stage Optimization

This script implements a comprehensive three-stage machine learning pipeline:

STAGE 1: FEATURE SELECTION
- Mutual information-based feature ranking
- Correlation analysis and redundancy removal
- Forward selection, backward elimination, and recursive feature elimination
- Cross-validation for robust feature evaluation

STAGE 2: BROAD INITIAL TESTING
- Comprehensive comparison of multiple classifier types
- Random Forest, SVM, Logistic Regression, k-NN, Decision Trees, Naive <PERSON>
- Default hyperparameters for fair initial comparison
- Cross-validation for reliable performance estimation

STAGE 3: DETAILED OPTIMIZATION
- GridSearchCV and RandomizedSearchCV for top-performing classifiers
- Extensive hyperparameter grids for each algorithm type
- Nested cross-validation for unbiased performance estimation
- Statistical significance testing between models

EVALUATION METRICS:
- Accuracy, Precision, Recall, F1-score
- <PERSON>'s Kappa, AUC-ROC (for binary classification)
- Confusion matrices and detailed per-class metrics
- Cross-validation with confidence intervals

USAGE:
1. Set TRAINING_DATA path to your .arff file
2. Configure optimization settings in the CONFIGURATION section
3. Run the script for complete automated optimization
4. Review comprehensive reports and model recommendations
5. Integrate optimal configurations into your ImageJ/FIJI scripts

Author: Enhanced ML Pipeline
Version: 3.0 - Production Ready
"""

# === IMPORTS ===
from ij import IJ
from java.io import File
import os

# === CONFIGURATION ===
# Paths to training data (.arff files) - SPECIFY YOUR PATHS HERE
TRAINING_DATA = "C:/Studium/Johann/Partikeluntersuchungen/63um/Training/4 Training mit O1-O3/combined_4 Training mit O1-O3_20250714_122950_total_summary.arff"

# Output directory for saving best models (will use same dir as training data if empty)
OUTPUT_DIR = ""

# === STAGE 1: FEATURE SELECTION CONFIGURATION ===
ENABLE_FEATURE_SELECTION = True
FEATURE_SELECTION_METHODS = ["mutual_info", "correlation", "forward", "backward", "recursive"]
MIN_FEATURES = 1
MAX_FEATURES = 15
FEATURE_CORRELATION_THRESHOLD = 0.95
FEATURE_SELECTION_CV_FOLDS = 5

# === STAGE 2: BROAD TESTING CONFIGURATION ===
ENABLE_BROAD_TESTING = True
BROAD_TESTING_CV_FOLDS = 5

# === STAGE 3: DETAILED OPTIMIZATION CONFIGURATION ===
ENABLE_DETAILED_OPTIMIZATION = True
TOP_MODELS_FOR_OPTIMIZATION = 3
HYPERPARAMETER_OPTIMIZATION_METHOD = "grid"  # "grid" or "random"
RANDOM_SEARCH_ITERATIONS = 50
DETAILED_CV_FOLDS = 10

# === EVALUATION CONFIGURATION ===
ENABLE_NESTED_CV = True
OUTER_CV_FOLDS = 5
INNER_CV_FOLDS = 3
CV_FOLDS = 10  # Standard cross-validation folds
ENABLE_STATISTICAL_TESTING = True
SIGNIFICANCE_LEVEL = 0.05
ENABLE_DETAILED_METRICS = True

# === CLASSIFICATION CONSTANTS ===
POROSITY_LABELS = ["NonPorous", "Porous"]
SHAPE_LABELS = ["Round", "Imperfect"]
RANDOM_SEED = 42

# Baseline feature sets (current configurations)
BASELINE_POROSITY_FEATURES = ["total_black_pixels"]
BASELINE_SHAPE_FEATURES = ["Area", "Solidity", "Convexity", "Roundness", "Feret_Ratio"]

# Complete feature pool for optimization
ALL_AVAILABLE_FEATURES = [
    "Area", "Solidity", "Convexity", "Roundness", "Feret_Ratio",
    "Concavity", "Robustness_O1", "Largest_Concavity_Index_O2",
    "Concavity_Robustness_Ratio_O3", "total_black_pixels",
    "total_concavities", "small_concavities", "moderate_concavities", "large_concavities"
]

# === WEKA SETUP ===
def setup_weka():
    """Setup Weka environment and import classes."""
    try:
        # Test basic Weka imports
        from weka.core.converters import ConverterUtils
        from weka.classifiers.evaluation import Evaluation
        return True
    except:
        IJ.log("ERROR: Weka classes not available. Please ensure Weka is properly installed.")
        return False

def load_training_data(data_path):
    """Load training data from ARFF file."""
    try:
        from weka.core.converters import ConverterUtils
        data_file = File(data_path)
        if not data_file.exists():
            IJ.log("ERROR: Training data file not found: " + data_path)
            return None

        data_source = ConverterUtils.DataSource(data_path)
        instances = data_source.getDataSet()

        if instances.classIndex() == -1:
            instances.setClassIndex(instances.numAttributes() - 1)

        IJ.log("Training data loaded: " + str(instances.numInstances()) + " instances")
        IJ.log("Number of attributes: " + str(instances.numAttributes()))

        # Log available features for verification
        IJ.log("Available features:")
        for i in range(instances.numAttributes() - 1):  # Exclude class attribute
            feature_name = instances.attribute(i).name()
            IJ.log("  " + str(i+1) + ". " + feature_name)

        # Log class attribute info
        class_attr = instances.classAttribute()
        IJ.log("Class attribute: " + class_attr.name())
        if class_attr.isNominal():
            IJ.log("Class values: " + str([str(class_attr.value(i)) for i in range(class_attr.numValues())]))

        return instances
    except Exception as e:
        IJ.log("ERROR: Failed to load training data: " + str(e))
        return None

def prepare_classification_data(instances, task_name):
    """Prepare data for specific classification task (Shape or Porosity)."""
    try:
        from weka.core import DenseInstance as WekaDenseInstance
        from weka.core import Instances as WekaInstances
        from weka.core import Attribute as WekaAttribute
        from java.util import ArrayList

        # Find the appropriate class attribute
        class_attr_index = -1
        if task_name.lower() == "porosity":
            # Look for Porosity attribute
            for i in range(instances.numAttributes()):
                if instances.attribute(i).name().lower() == "porosity":
                    class_attr_index = i
                    break
        elif task_name.lower() == "shape":
            # Look for Shape attribute
            for i in range(instances.numAttributes()):
                if instances.attribute(i).name().lower() == "shape":
                    class_attr_index = i
                    break

        if class_attr_index == -1:
            IJ.log("ERROR: Could not find appropriate class attribute for " + task_name)
            return None

        # Create new dataset with the correct class attribute
        attributes = ArrayList()

        # Add all feature attributes (excluding Group, Shape, Porosity)
        feature_count = 0
        for i in range(instances.numAttributes()):
            attr_name = instances.attribute(i).name().lower()
            if attr_name not in ["group", "shape", "porosity"]:
                attributes.add(instances.attribute(i).copy(instances.attribute(i).name()))
                feature_count += 1

        # Add the target class attribute
        target_class_attr = instances.attribute(class_attr_index)
        attributes.add(target_class_attr.copy(target_class_attr.name()))

        # Create new instances
        new_instances = WekaInstances(task_name + "_Data", attributes, instances.numInstances())
        new_instances.setClassIndex(new_instances.numAttributes() - 1)

        # Copy data
        for i in range(instances.numInstances()):
            original = instances.instance(i)
            new_instance = WekaDenseInstance(new_instances.numAttributes())
            new_instance.setDataset(new_instances)

            # Copy feature values
            feature_idx = 0
            for j in range(instances.numAttributes()):
                attr_name = instances.attribute(j).name().lower()
                if attr_name not in ["group", "shape", "porosity"]:
                    new_instance.setValue(feature_idx, original.value(j))
                    feature_idx += 1

            # Set class value
            new_instance.setClassValue(original.value(class_attr_index))
            new_instances.add(new_instance)

        IJ.log("Prepared " + task_name + " classification data: " + str(feature_count) + " features, " +
               str(new_instances.numInstances()) + " instances")

        return new_instances

    except Exception as e:
        IJ.log("ERROR: Failed to prepare " + task_name + " classification data: " + str(e))
        return None

def get_training_data_path_interactive(task_name):
    """Interactive dialog to select training data file."""
    try:
        from javax.swing import JFileChooser
        from javax.swing.filechooser import FileNameExtensionFilter
        
        chooser = JFileChooser()
        chooser.setDialogTitle("Select " + task_name + " Training Data (.arff file)")
        
        arff_filter = FileNameExtensionFilter("ARFF files (*.arff)", ["arff"])
        chooser.setFileFilter(arff_filter)
        
        if os.path.exists("C:\\Studium\\Johann\\Partikeluntersuchungen"):
            chooser.setCurrentDirectory(File("C:\\Studium\\Johann\\Partikeluntersuchungen"))
        
        result = chooser.showOpenDialog(None)
        if result == JFileChooser.APPROVE_OPTION:
            selected_file = chooser.getSelectedFile()
            return selected_file.getAbsolutePath()
        else:
            return None
    except:
        return None

def create_basic_classifiers():
    """Create basic classifiers for Stage 2: Broad Initial Testing."""
    try:
        from weka.classifiers.trees import RandomForest, J48
        from weka.classifiers.functions import SMO
        from weka.classifiers.bayes import NaiveBayes
        from weka.classifiers.lazy import IBk
        from weka.classifiers.meta import Bagging

        classifiers = []

        # Random Forest with default parameters
        rf = RandomForest()
        rf.setNumIterations(100)
        rf.setSeed(RANDOM_SEED)
        classifiers.append(("RandomForest", rf))

        # SVM with default parameters
        smo = SMO()
        classifiers.append(("SVM", smo))

        # k-NN with k=5
        knn = IBk()
        knn.setKNN(5)
        classifiers.append(("kNN", knn))

        # Decision Tree (J48)
        j48 = J48()
        classifiers.append(("DecisionTree", j48))

        # Naive Bayes
        nb = NaiveBayes()
        classifiers.append(("NaiveBayes", nb))

        # Bagging ensemble
        bagging = Bagging()
        bagging.setNumIterations(10)
        bagging.setSeed(RANDOM_SEED)
        classifiers.append(("Bagging", bagging))

        IJ.log("Created " + str(len(classifiers)) + " basic classifiers for broad testing")
        return classifiers

    except Exception as e:
        IJ.log("ERROR: Failed to create basic classifiers: " + str(e))
        return []

def create_optimized_classifiers(base_classifier_name):
    """Create optimized classifier configurations for Stage 3: Detailed Optimization."""
    try:
        from weka.classifiers.trees import RandomForest, J48
        from weka.classifiers.functions import SMO
        from weka.classifiers.lazy import IBk
        from weka.classifiers.meta import Bagging

        classifiers = []

        if base_classifier_name == "RandomForest":
            # Random Forest hyperparameter grid
            for n_trees in [50, 100, 200]:
                for max_depth in [0, 5, 10]:  # 0 = unlimited
                    for features in [0, 1]:  # 0 = sqrt, 1 = log2
                        rf = RandomForest()
                        rf.setNumIterations(n_trees)
                        rf.setMaxDepth(max_depth)
                        rf.setNumFeatures(features)
                        rf.setSeed(RANDOM_SEED)
                        name = "RF_t{}_d{}_f{}".format(n_trees, max_depth, features)
                        classifiers.append((name, rf))

        elif base_classifier_name == "SVM":
            # SVM hyperparameter grid
            for c in [0.1, 1.0, 10.0]:
                smo = SMO()
                smo.setC(c)
                name = "SVM_C{}".format(c)
                classifiers.append((name, smo))

        elif base_classifier_name == "kNN":
            # k-NN hyperparameter grid
            for k in [3, 5, 7, 11]:
                knn = IBk()
                knn.setKNN(k)
                name = "kNN_k{}".format(k)
                classifiers.append((name, knn))

        elif base_classifier_name == "DecisionTree":
            # Decision Tree hyperparameter grid
            for cf in [0.1, 0.25, 0.5]:
                for min_obj in [2, 5, 10]:
                    j48 = J48()
                    j48.setConfidenceFactor(cf)
                    j48.setMinNumObj(min_obj)
                    name = "J48_cf{}_min{}".format(cf, min_obj)
                    classifiers.append((name, j48))

        elif base_classifier_name == "Bagging":
            # Bagging hyperparameter grid
            for iterations in [10, 25, 50]:
                bagging = Bagging()
                bagging.setNumIterations(iterations)
                bagging.setSeed(RANDOM_SEED)
                name = "Bagging_{}".format(iterations)
                classifiers.append((name, bagging))

        IJ.log("Created " + str(len(classifiers)) + " optimized configurations for " + base_classifier_name)
        return classifiers

    except Exception as e:
        IJ.log("ERROR: Failed to create optimized classifiers for " + base_classifier_name + ": " + str(e))
        return []

def evaluate_classifier_comprehensive(classifier, instances, classifier_name):
    """Comprehensive evaluation with detailed metrics and nested CV."""
    try:
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random
        import math

        if ENABLE_NESTED_CV:
            return evaluate_classifier_nested_cv(classifier, instances, classifier_name)
        else:
            return evaluate_classifier_standard(classifier, instances, classifier_name)

    except Exception as e:
        IJ.log("ERROR: Comprehensive evaluation failed for " + classifier_name + ": " + str(e))
        return None

def evaluate_classifier_nested_cv(classifier, instances, classifier_name):
    """Evaluate classifier using nested cross-validation."""
    try:
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random
        import math

        outer_scores = []
        outer_detailed_metrics = []

        # Outer CV loop for model evaluation
        for fold in range(OUTER_CV_FOLDS):
            random = Random(RANDOM_SEED + fold)

            # Split data for outer CV
            instances.randomize(random)
            train_size = int(instances.numInstances() * (OUTER_CV_FOLDS - 1) / float(OUTER_CV_FOLDS))

            train_set = instances.trainCV(OUTER_CV_FOLDS, fold)
            test_set = instances.testCV(OUTER_CV_FOLDS, fold)

            # Inner CV for hyperparameter optimization (if applicable)
            # For now, use the classifier as-is
            classifier.buildClassifier(train_set)

            # Evaluate on test set
            evaluation = Evaluation(train_set)
            evaluation.evaluateModel(classifier, test_set)

            # Collect metrics
            accuracy = evaluation.pctCorrect() / 100.0
            outer_scores.append(accuracy)

            # Detailed metrics for this fold
            fold_metrics = extract_detailed_metrics(evaluation, instances)
            outer_detailed_metrics.append(fold_metrics)

        # Aggregate results
        mean_accuracy = sum(outer_scores) / len(outer_scores)
        std_accuracy = math.sqrt(sum((x - mean_accuracy) ** 2 for x in outer_scores) / len(outer_scores))

        # Aggregate detailed metrics
        aggregated_metrics = aggregate_fold_metrics(outer_detailed_metrics, instances)

        result = {
            'classifier_name': classifier_name,
            'accuracy': mean_accuracy,
            'accuracy_std': std_accuracy,
            'cv_scores': outer_scores,
            'evaluation_method': 'nested_cv'
        }

        # Add aggregated metrics manually (Jython 2.7 compatible)
        if aggregated_metrics:
            for key, value in aggregated_metrics.items():
                result[key] = value

        return result

    except Exception as e:
        IJ.log("ERROR: Nested CV evaluation failed for " + classifier_name + ": " + str(e))
        return None

def evaluate_classifier_standard(classifier, instances, classifier_name):
    """Standard cross-validation evaluation with comprehensive metrics."""
    try:
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random

        evaluation = Evaluation(instances)
        random = Random(RANDOM_SEED)
        evaluation.crossValidateModel(classifier, instances, CV_FOLDS, random)

        # Extract comprehensive metrics
        detailed_metrics = extract_detailed_metrics(evaluation, instances)

        result = {
            'classifier_name': classifier_name,
            'evaluation_method': 'standard_cv'
        }

        # Add detailed metrics manually (Jython 2.7 compatible)
        if detailed_metrics:
            for key, value in detailed_metrics.items():
                result[key] = value

        return result

    except Exception as e:
        IJ.log("ERROR: Standard evaluation failed for " + classifier_name + ": " + str(e))
        return None

def extract_detailed_metrics(evaluation, instances):
    """Extract comprehensive performance metrics from evaluation."""
    try:
        # Basic metrics
        accuracy = evaluation.pctCorrect() / 100.0

        # Per-class metrics
        class_metrics = {}
        for i in range(instances.numClasses()):
            class_name = instances.classAttribute().value(i)
            precision = evaluation.precision(i)
            recall = evaluation.recall(i)
            f1_score = evaluation.fMeasure(i)

            class_metrics[class_name] = {
                'precision': precision if not str(precision) == 'NaN' else 0.0,
                'recall': recall if not str(recall) == 'NaN' else 0.0,
                'f1_score': f1_score if not str(f1_score) == 'NaN' else 0.0
            }

        # Weighted metrics
        weighted_f1 = 0.0
        weighted_precision = 0.0
        weighted_recall = 0.0
        total_instances = instances.numInstances()

        for i in range(instances.numClasses()):
            class_count = 0
            for j in range(instances.numInstances()):
                if int(instances.instance(j).classValue()) == i:
                    class_count += 1
            weight = class_count / float(total_instances)
            class_name = instances.classAttribute().value(i)

            weighted_f1 += weight * class_metrics[class_name]['f1_score']
            weighted_precision += weight * class_metrics[class_name]['precision']
            weighted_recall += weight * class_metrics[class_name]['recall']

        # Additional metrics
        try:
            kappa = evaluation.kappa()
            mean_absolute_error = evaluation.meanAbsoluteError()
            root_mean_squared_error = evaluation.rootMeanSquaredError()
        except:
            kappa = 0.0
            mean_absolute_error = 0.0
            root_mean_squared_error = 0.0

        # ROC and AUC metrics (for binary classification)
        auc_metrics = {}
        if instances.numClasses() == 2:
            try:
                for i in range(instances.numClasses()):
                    class_name = instances.classAttribute().value(i)
                    auc = evaluation.areaUnderROC(i)
                    auc_metrics[class_name + '_AUC'] = auc if not str(auc) == 'NaN' else 0.0
            except:
                pass

        return {
            'accuracy': accuracy,
            'weighted_f1': weighted_f1,
            'weighted_precision': weighted_precision,
            'weighted_recall': weighted_recall,
            'kappa': kappa,
            'mean_absolute_error': mean_absolute_error,
            'root_mean_squared_error': root_mean_squared_error,
            'class_metrics': class_metrics,
            'auc_metrics': auc_metrics,
            'confusion_matrix': evaluation.confusionMatrix()
        }

    except Exception as e:
        IJ.log("ERROR: Failed to extract detailed metrics: " + str(e))
        return {
            'accuracy': 0.0,
            'weighted_f1': 0.0,
            'class_metrics': {},
            'confusion_matrix': None
        }

def aggregate_fold_metrics(fold_metrics_list, instances):
    """Aggregate metrics across CV folds."""
    try:
        import math

        # Aggregate basic metrics
        accuracies = [m['accuracy'] for m in fold_metrics_list]
        f1_scores = [m['weighted_f1'] for m in fold_metrics_list]

        mean_accuracy = sum(accuracies) / len(accuracies)
        std_accuracy = math.sqrt(sum((x - mean_accuracy) ** 2 for x in accuracies) / len(accuracies))

        mean_f1 = sum(f1_scores) / len(f1_scores)
        std_f1 = math.sqrt(sum((x - mean_f1) ** 2 for x in f1_scores) / len(f1_scores))

        # Aggregate class metrics
        aggregated_class_metrics = {}
        for i in range(instances.numClasses()):
            class_name = instances.classAttribute().value(i)
            precisions = [m['class_metrics'].get(class_name, {}).get('precision', 0.0) for m in fold_metrics_list]
            recalls = [m['class_metrics'].get(class_name, {}).get('recall', 0.0) for m in fold_metrics_list]
            f1s = [m['class_metrics'].get(class_name, {}).get('f1_score', 0.0) for m in fold_metrics_list]

            aggregated_class_metrics[class_name] = {
                'precision': sum(precisions) / len(precisions),
                'recall': sum(recalls) / len(recalls),
                'f1_score': sum(f1s) / len(f1s)
            }

        return {
            'accuracy': mean_accuracy,
            'accuracy_std': std_accuracy,
            'weighted_f1': mean_f1,
            'weighted_f1_std': std_f1,
            'class_metrics': aggregated_class_metrics
        }

    except Exception as e:
        IJ.log("ERROR: Failed to aggregate fold metrics: " + str(e))
        return {
            'accuracy': 0.0,
            'weighted_f1': 0.0,
            'class_metrics': {}
        }

def find_best_models_for_task(training_data, task_name, baseline_features):
    """Complete three-stage machine learning pipeline."""
    IJ.log("")
    IJ.log("=" * 60)
    IJ.log("THREE-STAGE ML PIPELINE FOR " + task_name.upper())
    IJ.log("=" * 60)

    # STAGE 1: FEATURE SELECTION
    IJ.log("")
    IJ.log("STAGE 1: FEATURE SELECTION")
    IJ.log("=" * 40)

    optimal_features, feature_score = stage1_feature_selection(training_data, task_name, baseline_features)

    # Extract optimal features
    filtered_instances = extract_required_features(training_data, optimal_features)
    if filtered_instances is None:
        IJ.log("ERROR: Failed to extract optimal features for " + task_name)
        return [], optimal_features

    # STAGE 2: BROAD INITIAL TESTING
    IJ.log("")
    IJ.log("STAGE 2: BROAD INITIAL TESTING")
    IJ.log("=" * 40)

    broad_results = stage2_broad_testing(filtered_instances, task_name)

    if not broad_results:
        IJ.log("ERROR: No classifiers passed broad testing")
        return [], optimal_features

    # STAGE 3: DETAILED OPTIMIZATION
    IJ.log("")
    IJ.log("STAGE 3: DETAILED OPTIMIZATION")
    IJ.log("=" * 40)

    final_results = stage3_detailed_optimization(filtered_instances, broad_results, task_name)

    # Sort by weighted F1 score
    final_results.sort(key=lambda x: x.get('weighted_f1', 0.0), reverse=True)

    # Display final results
    IJ.log("")
    IJ.log("FINAL RESULTS FOR " + task_name + " (with " + str(len(optimal_features)) + " features):")
    for i, result in enumerate(final_results[:5]):
        IJ.log("  " + str(i+1) + ". " + result['classifier_name'] +
               " (F1: " + str(round(result.get('weighted_f1', 0.0), 3)) +
               ", Acc: " + str(round(result.get('accuracy', 0.0), 3)) + ")")

    return final_results, optimal_features

def stage1_feature_selection(instances, task_name, baseline_features):
    """Stage 1: Comprehensive feature selection using multiple methods."""
    if not ENABLE_FEATURE_SELECTION:
        IJ.log("Feature selection disabled, using baseline features")
        return baseline_features, 0.0

    # Get available features
    available_features = get_available_features(instances)
    valid_features = [f for f in available_features if f in ALL_AVAILABLE_FEATURES]

    if not valid_features:
        IJ.log("No valid features found, using baseline")
        return baseline_features, 0.0

    IJ.log("Available features: " + str(len(valid_features)))

    # Method 1: Mutual Information
    mi_features, mi_score = mutual_information_selection(instances, valid_features, task_name)

    # Method 2: Correlation Analysis
    corr_features = correlation_based_selection(instances, valid_features)

    # Method 3: Forward Selection
    forward_features, forward_score = forward_selection(instances, valid_features, task_name)

    # Method 4: Backward Elimination
    backward_features, backward_score = backward_elimination(instances, valid_features, task_name)

    # Method 5: Recursive Feature Elimination
    rfe_features, rfe_score = recursive_feature_elimination(instances, valid_features, task_name)

    # Compare all methods and select best
    methods = [
        ("mutual_info", mi_features, mi_score),
        ("correlation", corr_features, 0.0),
        ("forward", forward_features, forward_score),
        ("backward", backward_features, backward_score),
        ("recursive", rfe_features, rfe_score)
    ]

    best_method, best_features, best_score = max(methods, key=lambda x: x[2])

    IJ.log("Best feature selection method: " + best_method)
    IJ.log("Selected features (" + str(len(best_features)) + "): " + ", ".join(best_features))

    return best_features, best_score

def stage2_broad_testing(instances, task_name):
    """Stage 2: Broad initial testing of multiple classifier types."""
    if not ENABLE_BROAD_TESTING:
        IJ.log("Broad testing disabled")
        return []

    # Get basic classifiers for comparison
    classifiers = create_basic_classifiers()
    if not classifiers:
        IJ.log("ERROR: No classifiers available for broad testing")
        return []

    results = []
    IJ.log("Testing " + str(len(classifiers)) + " classifier types...")

    for classifier_name, classifier in classifiers:
        try:
            result = evaluate_classifier_standard(classifier, instances, classifier_name)
            if result:
                results.append(result)
                IJ.log("  " + classifier_name + ": F1=" + str(round(result.get('weighted_f1', 0.0), 3)) +
                       ", Acc=" + str(round(result.get('accuracy', 0.0), 3)))
        except Exception as e:
            IJ.log("  " + classifier_name + " failed: " + str(e))

    # Sort by weighted F1 score
    results.sort(key=lambda x: x.get('weighted_f1', 0.0), reverse=True)

    IJ.log("Broad testing completed: " + str(len(results)) + " successful evaluations")
    return results

def stage3_detailed_optimization(instances, broad_results, task_name):
    """Stage 3: Detailed hyperparameter optimization for top performers."""
    if not ENABLE_DETAILED_OPTIMIZATION or not broad_results:
        IJ.log("Detailed optimization disabled or no results from broad testing")
        return broad_results

    # Select top performers for detailed optimization
    top_performers = broad_results[:TOP_MODELS_FOR_OPTIMIZATION]

    IJ.log("Optimizing top " + str(len(top_performers)) + " performers:")
    for i, result in enumerate(top_performers):
        IJ.log("  " + str(i+1) + ". " + result['classifier_name'] +
               " (F1: " + str(round(result.get('weighted_f1', 0.0), 3)) + ")")

    optimized_results = []

    for result in top_performers:
        classifier_name = result['classifier_name']
        try:
            # Create optimized configurations for this classifier type
            optimized_classifiers = create_optimized_classifiers(classifier_name)

            best_config = result  # Default to original
            best_score = result.get('weighted_f1', 0.0)

            IJ.log("  Testing " + str(len(optimized_classifiers)) + " configurations for " + classifier_name)

            for opt_name, opt_classifier in optimized_classifiers:
                try:
                    opt_result = evaluate_classifier_standard(opt_classifier, instances, opt_name)
                    if opt_result and opt_result.get('weighted_f1', 0.0) > best_score:
                        best_config = opt_result
                        best_score = opt_result.get('weighted_f1', 0.0)
                except:
                    continue

            optimized_results.append(best_config)
            IJ.log("    Best config: " + best_config['classifier_name'] +
                   " (F1: " + str(round(best_score, 3)) + ")")

        except Exception as e:
            IJ.log("  Optimization failed for " + classifier_name + ": " + str(e))
            optimized_results.append(result)

    # Add remaining results without optimization
    remaining_results = broad_results[TOP_MODELS_FOR_OPTIMIZATION:]
    final_results = optimized_results + remaining_results

    return final_results

def mutual_information_selection(instances, available_features, task_name):
    """Feature selection using mutual information."""
    try:
        # Simplified mutual information - use Random Forest feature importance as proxy
        IJ.log("  Performing mutual information feature selection...")

        # Test different feature subset sizes
        best_features = available_features[:5]  # Default to first 5
        best_score = 0.0

        for num_features in range(MIN_FEATURES, min(MAX_FEATURES + 1, len(available_features) + 1)):
            test_features = available_features[:num_features]
            filtered_instances = extract_required_features(instances, test_features)
            if filtered_instances:
                score = quick_evaluate_features(filtered_instances)
                if score > best_score:
                    best_score = score
                    best_features = test_features

        IJ.log("    Mutual info selection: " + str(len(best_features)) + " features, score: " + str(round(best_score, 3)))
        return best_features, best_score

    except Exception as e:
        IJ.log("    Mutual information selection failed: " + str(e))
        return available_features[:5], 0.0

def correlation_based_selection(instances, available_features):
    """Feature selection based on correlation analysis."""
    try:
        IJ.log("  Performing correlation-based feature selection...")

        # Remove highly correlated features
        filtered_features = filter_correlated_features(instances, available_features)

        IJ.log("    Correlation filtering: " + str(len(filtered_features)) + " features")
        return filtered_features

    except Exception as e:
        IJ.log("    Correlation-based selection failed: " + str(e))
        return available_features

def forward_selection(instances, available_features, task_name):
    """Forward feature selection."""
    try:
        IJ.log("  Performing forward feature selection...")

        selected_features = []
        remaining_features = available_features[:]
        best_score = 0.0

        while remaining_features and len(selected_features) < MAX_FEATURES:
            best_feature = None
            best_current_score = 0.0

            for feature in remaining_features:
                test_features = selected_features + [feature]
                filtered_instances = extract_required_features(instances, test_features)
                if filtered_instances:
                    score = quick_evaluate_features(filtered_instances)
                    if score > best_current_score:
                        best_current_score = score
                        best_feature = feature

            if best_feature and best_current_score > best_score:
                selected_features.append(best_feature)
                remaining_features.remove(best_feature)
                best_score = best_current_score
            else:
                break

        IJ.log("    Forward selection: " + str(len(selected_features)) + " features, score: " + str(round(best_score, 3)))
        return selected_features, best_score

    except Exception as e:
        IJ.log("    Forward selection failed: " + str(e))
        return available_features[:5], 0.0

def backward_elimination(instances, available_features, task_name):
    """Backward feature elimination."""
    try:
        IJ.log("  Performing backward feature elimination...")

        current_features = available_features[:MAX_FEATURES]  # Start with max features
        best_score = 0.0

        # Get baseline score
        filtered_instances = extract_required_features(instances, current_features)
        if filtered_instances:
            best_score = quick_evaluate_features(filtered_instances)

        while len(current_features) > MIN_FEATURES:
            worst_feature = None
            best_current_score = 0.0

            for feature in current_features:
                test_features = [f for f in current_features if f != feature]
                filtered_instances = extract_required_features(instances, test_features)
                if filtered_instances:
                    score = quick_evaluate_features(filtered_instances)
                    if score > best_current_score:
                        best_current_score = score
                        worst_feature = feature

            if worst_feature and best_current_score > best_score:
                current_features.remove(worst_feature)
                best_score = best_current_score
            else:
                break

        IJ.log("    Backward elimination: " + str(len(current_features)) + " features, score: " + str(round(best_score, 3)))
        return current_features, best_score

    except Exception as e:
        IJ.log("    Backward elimination failed: " + str(e))
        return available_features[:5], 0.0

def recursive_feature_elimination(instances, available_features, task_name):
    """Recursive feature elimination using feature importance."""
    try:
        IJ.log("  Performing recursive feature elimination...")

        current_features = available_features[:MAX_FEATURES]
        feature_rankings = {}
        rank = len(current_features)

        while len(current_features) > MIN_FEATURES:
            # Calculate feature importance
            importance_scores = calculate_feature_importance(instances, current_features)

            if not importance_scores:
                break

            # Remove least important feature
            least_important = min(importance_scores.keys(), key=lambda x: importance_scores[x])
            feature_rankings[least_important] = rank
            current_features.remove(least_important)
            rank -= 1

        # Assign remaining features top ranks
        for feature in current_features:
            feature_rankings[feature] = rank
            rank -= 1

        # Select top features
        sorted_features = sorted(feature_rankings.keys(), key=lambda x: feature_rankings[x])
        selected_features = sorted_features[:min(MAX_FEATURES, len(sorted_features))]

        # Evaluate final set
        filtered_instances = extract_required_features(instances, selected_features)
        final_score = quick_evaluate_features(filtered_instances) if filtered_instances else 0.0

        IJ.log("    Recursive elimination: " + str(len(selected_features)) + " features, score: " + str(round(final_score, 3)))
        return selected_features, final_score

    except Exception as e:
        IJ.log("    Recursive elimination failed: " + str(e))
        return available_features[:5], 0.0

# Removed duplicate functions - functionality moved to stage2_broad_testing and stage3_detailed_optimization

def extract_required_features(instances, required_features):
    """Extract only required features from instances."""
    try:
        from weka.core import DenseInstance as WekaDenseInstance
        from weka.core import Instances as WekaInstances
        from weka.core import Attribute as WekaAttribute
        from java.util import ArrayList

        # Find feature indices
        feature_indices = []
        for req_feat in required_features:
            for i in range(instances.numAttributes() - 1):
                if instances.attribute(i).name().lower() == req_feat.lower():
                    feature_indices.append(i)
                    break
            else:
                IJ.log("ERROR: Missing feature: " + req_feat)
                return None

        # Create new dataset
        attributes = ArrayList()
        for name in required_features:
            attributes.add(WekaAttribute(name.replace(" ", "_")))

        # Copy class attribute
        class_attr = instances.classAttribute()
        attributes.add(class_attr.copy(class_attr.name()))

        new_instances = WekaInstances("FilteredData", attributes, instances.numInstances())
        new_instances.setClassIndex(new_instances.numAttributes() - 1)

        # Copy instances
        for i in range(instances.numInstances()):
            original = instances.instance(i)
            new_instance = WekaDenseInstance(new_instances.numAttributes())
            new_instance.setDataset(new_instances)

            for j, idx in enumerate(feature_indices):
                new_instance.setValue(j, original.value(idx))

            new_instance.setClassValue(original.classValue())
            new_instances.add(new_instance)

        return new_instances

    except Exception as e:
        IJ.log("ERROR: Feature extraction failed: " + str(e))
        return None

def get_available_features(instances):
    """Get list of available features from the dataset."""
    available_features = []
    for i in range(instances.numAttributes() - 1):  # Exclude class attribute
        feature_name = instances.attribute(i).name()
        available_features.append(feature_name)
    return available_features

def calculate_feature_importance(instances, feature_subset):
    """Calculate feature importance using a simple Random Forest."""
    try:
        from weka.classifiers.trees import RandomForest
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random

        # Extract features
        filtered_instances = extract_required_features(instances, feature_subset)
        if filtered_instances is None:
            return {}

        # Train Random Forest
        rf = RandomForest()
        rf.setNumIterations(50)
        rf.setSeed(RANDOM_SEED)
        rf.buildClassifier(filtered_instances)

        # Evaluate with cross-validation
        evaluation = Evaluation(filtered_instances)
        random = Random(RANDOM_SEED)
        evaluation.crossValidateModel(rf, filtered_instances, FEATURE_SELECTION_CV_FOLDS, random)

        accuracy = evaluation.pctCorrect() / 100.0

        # Return importance score (accuracy as proxy)
        importance = {}
        for feature in feature_subset:
            importance[feature] = accuracy / len(feature_subset)  # Distribute score

        return importance

    except Exception as e:
        IJ.log("Warning: Feature importance calculation failed: " + str(e))
        return {}

def filter_correlated_features(instances, available_features):
    """Remove highly correlated features."""
    try:
        # Simple correlation filtering - remove features with high correlation
        filtered_features = []
        correlation_threshold = 0.9

        for i, feature1 in enumerate(available_features):
            is_redundant = False
            for j, feature2 in enumerate(filtered_features):
                # Simple correlation check (placeholder)
                if feature1.lower() in feature2.lower() or feature2.lower() in feature1.lower():
                    is_redundant = True
                    break

            if not is_redundant:
                filtered_features.append(feature1)

        return filtered_features if filtered_features else available_features[:5]

    except Exception as e:
        IJ.log("    Correlation filtering failed: " + str(e))
        return available_features

def evaluate_classifier_standard(classifier, instances, classifier_name):
    """Standard classifier evaluation with cross-validation."""
    try:
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random

        evaluation = Evaluation(instances)
        random = Random(RANDOM_SEED)
        evaluation.crossValidateModel(classifier, instances, CV_FOLDS, random)

        # Extract metrics
        accuracy = evaluation.pctCorrect() / 100.0
        precision = evaluation.weightedPrecision()
        recall = evaluation.weightedRecall()
        f1 = evaluation.weightedFMeasure()

        return {
            'classifier_name': classifier_name,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'weighted_f1': f1,
            'kappa': evaluation.kappa()
        }

    except Exception as e:
        IJ.log("    Evaluation failed for " + classifier_name + ": " + str(e))
        return None

def quick_evaluate_features(instances):
    """Quick evaluation of feature set using Random Forest."""
    try:
        from weka.classifiers.trees import RandomForest
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random

        rf = RandomForest()
        rf.setNumIterations(10)  # Faster evaluation
        rf.setSeed(RANDOM_SEED)

        evaluation = Evaluation(instances)
        random = Random(RANDOM_SEED)
        evaluation.crossValidateModel(rf, instances, FEATURE_SELECTION_CV_FOLDS, random)

        return evaluation.pctCorrect() / 100.0

    except Exception as e:
        return 0.0

def calculate_feature_correlations(instances):
    """Calculate correlation matrix for features."""
    try:
        import math

        num_features = instances.numAttributes() - 1  # Exclude class
        correlations = {}

        for i in range(num_features):
            for j in range(i + 1, num_features):
                feature1_name = instances.attribute(i).name()
                feature2_name = instances.attribute(j).name()

                # Calculate Pearson correlation
                values1 = []
                values2 = []

                for k in range(instances.numInstances()):
                    instance = instances.instance(k)
                    values1.append(instance.value(i))
                    values2.append(instance.value(j))

                correlation = calculate_pearson_correlation(values1, values2)
                correlations[(feature1_name, feature2_name)] = abs(correlation)

        return correlations

    except Exception as e:
        IJ.log("Warning: Failed to calculate correlations: " + str(e))
        return {}

def calculate_pearson_correlation(x, y):
    """Calculate Pearson correlation coefficient."""
    try:
        import math

        n = len(x)
        if n == 0:
            return 0.0

        sum_x = sum(x)
        sum_y = sum(y)
        sum_x2 = sum(xi * xi for xi in x)
        sum_y2 = sum(yi * yi for yi in y)
        sum_xy = sum(xi * yi for xi, yi in zip(x, y))

        numerator = n * sum_xy - sum_x * sum_y
        denominator = math.sqrt((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y))

        if denominator == 0:
            return 0.0

        return numerator / denominator

    except:
        return 0.0

def filter_correlated_features(instances, features, threshold=None):
    """Remove highly correlated features."""
    if threshold is None:
        threshold = FEATURE_CORRELATION_THRESHOLD

    try:
        IJ.log("  Filtering correlated features (threshold: " + str(threshold) + ")")

        correlations = calculate_feature_correlations(instances)
        features_to_remove = set()

        for (feat1, feat2), corr in correlations.items():
            if corr > threshold and feat1 in features and feat2 in features:
                # Remove the feature that appears later in the list
                if features.index(feat1) > features.index(feat2):
                    features_to_remove.add(feat1)
                else:
                    features_to_remove.add(feat2)

        filtered_features = [f for f in features if f not in features_to_remove]

        if len(features_to_remove) > 0:
            IJ.log("  Removed " + str(len(features_to_remove)) + " correlated features: " + ", ".join(features_to_remove))

        return filtered_features

    except Exception as e:
        IJ.log("  Warning: Correlation filtering failed: " + str(e))
        return features

def optimize_feature_selection(instances, task_name, baseline_features):
    """Perform comprehensive feature selection optimization with correlation filtering."""
    if not ENABLE_FEATURE_SELECTION:
        IJ.log("Feature selection disabled, using baseline features for " + task_name)
        return baseline_features, 0.0

    IJ.log("")
    IJ.log("=" * 50)
    IJ.log("FEATURE SELECTION OPTIMIZATION FOR " + task_name.upper())
    IJ.log("=" * 50)

    # Get available features from dataset
    available_features = get_available_features(instances)

    # Filter available features to only include those in our known feature pool
    valid_features = []
    for feature in available_features:
        # Case-insensitive matching
        for known_feature in ALL_AVAILABLE_FEATURES:
            if feature.lower() == known_feature.lower():
                valid_features.append(feature)
                break

    if not valid_features:
        IJ.log("ERROR: No valid features found in dataset for " + task_name)
        return baseline_features, 0.0

    IJ.log("Available features for optimization: " + str(len(valid_features)))
    IJ.log("Features: " + ", ".join(valid_features))

    # Apply correlation filtering to reduce feature space
    filtered_features = filter_correlated_features(instances, valid_features)
    IJ.log("Features after correlation filtering: " + str(len(filtered_features)))

    # Evaluate baseline performance
    baseline_score = 0.0
    try:
        baseline_instances = extract_required_features(instances, baseline_features)
        if baseline_instances:
            baseline_score = quick_evaluate_features(baseline_instances)
            IJ.log("Baseline performance (" + str(len(baseline_features)) + " features): " + str(round(baseline_score, 3)))
    except:
        pass

    # Test different feature selection methods
    best_features = baseline_features
    best_score = baseline_score
    best_method = "baseline"

    results = []

    for method in FEATURE_SELECTION_METHODS:
        try:
            if method == "forward":
                features, score = forward_feature_selection_enhanced(instances, filtered_features, task_name)
            elif method == "backward":
                features, score = backward_feature_elimination_enhanced(instances, filtered_features, task_name)
            elif method == "recursive":
                features, score = recursive_feature_elimination(instances, filtered_features, task_name)
            else:
                continue

            results.append({
                'method': method,
                'features': features,
                'score': score,
                'num_features': len(features)
            })

            IJ.log("  " + method.capitalize() + " selection: " + str(len(features)) + " features, score: " + str(round(score, 3)))

            if score > best_score:
                best_features = features
                best_score = score
                best_method = method

        except Exception as e:
            IJ.log("  " + method.capitalize() + " selection failed: " + str(e))

    # Feature count optimization (simplified)
    if best_features != baseline_features:
        IJ.log("  Feature optimization completed")

    # Report results
    IJ.log("")
    IJ.log("FEATURE SELECTION RESULTS FOR " + task_name.upper() + ":")
    IJ.log("Best method: " + best_method + " (score: " + str(round(best_score, 3)) + ")")
    IJ.log("Best features (" + str(len(best_features)) + "): " + ", ".join(best_features))

    if best_score > baseline_score:
        improvement = ((best_score - baseline_score) / baseline_score) * 100
        IJ.log("Improvement over baseline: +" + str(round(improvement, 1)) + "%")
    else:
        IJ.log("No improvement over baseline, using baseline features")
        best_features = baseline_features
        best_score = baseline_score

    return best_features, best_score

def forward_feature_selection_enhanced(instances, available_features, task_name):
    """Enhanced forward feature selection with fine-grained steps."""
    IJ.log("  Starting enhanced forward feature selection for " + task_name)

    selected_features = []
    remaining_features = available_features[:]
    best_score = 0.0

    while remaining_features and len(selected_features) < MAX_FEATURES:
        best_feature = None
        best_current_score = 0.0

        # Test adding each remaining feature
        for feature in remaining_features:
            test_features = selected_features + [feature]

            try:
                filtered_instances = extract_required_features(instances, test_features)
                if filtered_instances is None:
                    continue

                score = quick_evaluate_features(filtered_instances)

                if score > best_current_score:
                    best_current_score = score
                    best_feature = feature

            except Exception as e:
                continue

        # Add best feature if it improves performance
        if best_feature and best_current_score > best_score:
            selected_features.append(best_feature)
            remaining_features.remove(best_feature)
            best_score = best_current_score
            IJ.log("    Added feature: " + best_feature + " (score: " + str(round(best_score, 3)) + ")")
        else:
            break  # No improvement found

    IJ.log("  Enhanced forward selection completed: " + str(len(selected_features)) + " features")
    return selected_features, best_score

def backward_feature_elimination_enhanced(instances, available_features, task_name):
    """Enhanced backward feature elimination with fine-grained steps."""
    IJ.log("  Starting enhanced backward feature elimination for " + task_name)

    # Start with all available features (up to MAX_FEATURES)
    current_features = available_features[:MAX_FEATURES]
    best_score = 0.0

    # Get baseline score
    try:
        filtered_instances = extract_required_features(instances, current_features)
        if filtered_instances is not None:
            best_score = quick_evaluate_features(filtered_instances)
    except:
        best_score = 0.0

    while len(current_features) > MIN_FEATURES:
        worst_feature = None
        best_current_score = 0.0

        # Test removing each feature
        for feature in current_features:
            test_features = [f for f in current_features if f != feature]

            try:
                filtered_instances = extract_required_features(instances, test_features)
                if filtered_instances is None:
                    continue

                score = quick_evaluate_features(filtered_instances)

                if score > best_current_score:
                    best_current_score = score
                    worst_feature = feature

            except Exception as e:
                continue

        # Remove worst feature if it improves performance
        if worst_feature and best_current_score > best_score:
            current_features.remove(worst_feature)
            best_score = best_current_score
            IJ.log("    Removed feature: " + worst_feature + " (score: " + str(round(best_score, 3)) + ")")
        else:
            break  # No improvement found

    IJ.log("  Enhanced backward elimination completed: " + str(len(current_features)) + " features")
    return current_features, best_score

# Removed duplicate enhanced functions - functionality integrated into main stage functions

def save_best_model(classifier_name, classifier, training_data, task_name, output_dir):
    """Train and save the best model."""
    try:
        from weka.core import SerializationHelper as WekaSerializationHelper

        # Train the classifier on full dataset
        classifier.buildClassifier(training_data)

        # Generate filename
        safe_name = classifier_name.replace(" ", "_").replace("/", "_")
        model_filename = task_name.lower() + "_best_" + safe_name + ".model"
        model_path = os.path.join(output_dir, model_filename)

        # Save model
        WekaSerializationHelper.write(model_path, classifier)
        IJ.log("Best " + task_name + " model saved: " + model_path)
        return model_path

    except Exception as e:
        IJ.log("ERROR: Failed to save model: " + str(e))
        return None

def save_enhanced_results_report(porosity_results, shape_results, porosity_features, shape_features, output_dir):
    """Save comprehensive results report including feature selection results."""
    try:
        from java.io import FileWriter, BufferedWriter
        from java.io import File

        # Save detailed model performance report
        report_path = os.path.join(output_dir, "comprehensive_optimization_report.csv")
        writer = BufferedWriter(FileWriter(File(report_path)))

        # Write header
        header = "Task,Rank,Classifier,Accuracy,Weighted_F1,Num_Features,Features,Class,Precision,Recall,F1_Score\n"
        writer.write(header)

        # Write porosity results
        for rank, result in enumerate(porosity_results, 1):
            features_str = ";".join(result.get('optimal_features', porosity_features))
            num_features = result.get('num_features', len(porosity_features))

            for class_name, metrics in result['class_metrics'].items():
                row = "Porosity," + str(rank) + "," + result['classifier_name'] + ","
                row += str(result['accuracy']) + "," + str(result['weighted_f1']) + ","
                row += str(num_features) + ",\"" + features_str + "\","
                row += class_name + "," + str(metrics['precision']) + ","
                row += str(metrics['recall']) + "," + str(metrics['f1_score']) + "\n"
                writer.write(row)

        # Write shape results
        for rank, result in enumerate(shape_results, 1):
            features_str = ";".join(result.get('optimal_features', shape_features))
            num_features = result.get('num_features', len(shape_features))

            for class_name, metrics in result['class_metrics'].items():
                row = "Shape," + str(rank) + "," + result['classifier_name'] + ","
                row += str(result['accuracy']) + "," + str(result['weighted_f1']) + ","
                row += str(num_features) + ",\"" + features_str + "\","
                row += class_name + "," + str(metrics['precision']) + ","
                row += str(metrics['recall']) + "," + str(metrics['f1_score']) + "\n"
                writer.write(row)

        writer.close()
        IJ.log("Comprehensive report saved: " + report_path)

        # Save feature selection summary
        feature_summary_path = os.path.join(output_dir, "feature_selection_summary.csv")
        feature_writer = BufferedWriter(FileWriter(File(feature_summary_path)))

        # Write feature summary header
        feature_header = "Task,Baseline_Features,Optimal_Features,Baseline_Count,Optimal_Count,Improvement\n"
        feature_writer.write(feature_header)

        # Porosity feature summary
        baseline_porosity = ";".join(BASELINE_POROSITY_FEATURES)
        optimal_porosity = ";".join(porosity_features)
        porosity_improvement = "N/A"
        if porosity_results:
            baseline_score = porosity_results[0].get('feature_score', 0.0)
            if baseline_score > 0:
                porosity_improvement = str(round(baseline_score * 100, 1)) + "%"

        porosity_row = "Porosity,\"" + baseline_porosity + "\",\"" + optimal_porosity + "\","
        porosity_row += str(len(BASELINE_POROSITY_FEATURES)) + "," + str(len(porosity_features)) + ","
        porosity_row += porosity_improvement + "\n"
        feature_writer.write(porosity_row)

        # Shape feature summary
        baseline_shape = ";".join(BASELINE_SHAPE_FEATURES)
        optimal_shape = ";".join(shape_features)
        shape_improvement = "N/A"
        if shape_results:
            baseline_score = shape_results[0].get('feature_score', 0.0)
            if baseline_score > 0:
                shape_improvement = str(round(baseline_score * 100, 1)) + "%"

        shape_row = "Shape,\"" + baseline_shape + "\",\"" + optimal_shape + "\","
        shape_row += str(len(BASELINE_SHAPE_FEATURES)) + "," + str(len(shape_features)) + ","
        shape_row += shape_improvement + "\n"
        feature_writer.write(shape_row)

        feature_writer.close()
        IJ.log("Feature selection summary saved: " + feature_summary_path)
        return True

    except Exception as e:
        IJ.log("ERROR: Failed to save enhanced report: " + str(e))
        return False

def main():
    """Main function to run model selection and optimization."""
    # Clear log window
    IJ.log("\\Clear")
    IJ.log("=" * 60)
    IJ.log("Model Selection and Optimization - Starting...")
    IJ.log("=" * 60)

    # Basic startup test
    try:
        IJ.log("Testing basic functionality...")
        test_file = File(".")
        IJ.log("File operations: OK")
    except Exception as e:
        IJ.log("ERROR: Basic functionality test failed: " + str(e))
        return False

    # Setup Weka
    IJ.log("Setting up Weka...")
    if not setup_weka():
        return False
    IJ.log("Weka setup: OK")

    # Validate configuration
    IJ.log("Validating configuration...")
    if MIN_FEATURES < 1 or MAX_FEATURES < MIN_FEATURES:
        IJ.log("ERROR: Invalid feature configuration")
        return False
    IJ.log("Configuration: OK")

    # Get training data path (same file for both porosity and shape)
    training_data_path = TRAINING_DATA
    if not training_data_path or not File(training_data_path).exists():
        IJ.log("Training data not found or not specified. Opening file selection...")
        training_data_path = get_training_data_path_interactive("Combined Training Data")
        if not training_data_path:
            IJ.log("ERROR: No training data selected")
            return False

    # Set output directory
    output_dir = OUTPUT_DIR
    if not output_dir:
        output_dir = os.path.dirname(training_data_path)

    # Load training data
    IJ.log("Loading training data...")
    raw_data = load_training_data(training_data_path)
    if raw_data is None:
        return False

    # Prepare separate datasets for porosity and shape classification
    IJ.log("Preparing classification datasets...")
    porosity_data = prepare_classification_data(raw_data, "Porosity")
    if porosity_data is None:
        return False

    shape_data = prepare_classification_data(raw_data, "Shape")
    if shape_data is None:
        return False

    # Find best models and optimal features for each task
    porosity_results, optimal_porosity_features = find_best_models_for_task(porosity_data, "Porosity", BASELINE_POROSITY_FEATURES)
    shape_results, optimal_shape_features = find_best_models_for_task(shape_data, "Shape", BASELINE_SHAPE_FEATURES)

    if not porosity_results or not shape_results:
        IJ.log("ERROR: Model evaluation failed")
        return False

    # Save best models
    IJ.log("")
    IJ.log("=" * 60)
    IJ.log("SAVING BEST MODELS WITH OPTIMAL FEATURES")
    IJ.log("=" * 60)

    # Get best classifiers and retrain on filtered data
    best_porosity = porosity_results[0]
    best_shape = shape_results[0]

    # Recreate and train best classifiers
    classifiers = create_basic_classifiers()
    classifier_dict = dict(classifiers)

    if best_porosity['classifier_name'] in classifier_dict:
        porosity_filtered = extract_required_features(porosity_data, optimal_porosity_features)
        save_best_model(best_porosity['classifier_name'],
                       classifier_dict[best_porosity['classifier_name']],
                       porosity_filtered, "Porosity", output_dir)

    if best_shape['classifier_name'] in classifier_dict:
        shape_filtered = extract_required_features(shape_data, optimal_shape_features)
        save_best_model(best_shape['classifier_name'],
                       classifier_dict[best_shape['classifier_name']],
                       shape_filtered, "Shape", output_dir)

    # Save comprehensive reports
    save_enhanced_results_report(porosity_results, shape_results, optimal_porosity_features, optimal_shape_features, output_dir)

    # Display comprehensive final recommendations
    IJ.log("")
    IJ.log("=" * 60)
    IJ.log("COMPREHENSIVE OPTIMIZATION RESULTS")
    IJ.log("=" * 60)

    IJ.log("POROSITY CLASSIFICATION:")
    IJ.log("  Best Model: " + best_porosity['classifier_name'])
    IJ.log("  Accuracy: " + str(round(best_porosity.get('accuracy', 0.0), 3)))
    IJ.log("  Weighted F1: " + str(round(best_porosity.get('weighted_f1', 0.0), 3)))

    # Additional metrics if available
    if 'weighted_precision' in best_porosity:
        IJ.log("  Weighted Precision: " + str(round(best_porosity['weighted_precision'], 3)))
        IJ.log("  Weighted Recall: " + str(round(best_porosity['weighted_recall'], 3)))

    if 'kappa' in best_porosity:
        IJ.log("  Cohen's Kappa: " + str(round(best_porosity['kappa'], 3)))

    if 'accuracy_std' in best_porosity:
        IJ.log("  Accuracy Std Dev: " + str(round(best_porosity['accuracy_std'], 3)))

    if 'statistical_significance' in best_porosity:
        IJ.log("  Statistical Significance: " + best_porosity['statistical_significance'])

    IJ.log("  Optimal Features (" + str(len(optimal_porosity_features)) + "): " + ", ".join(optimal_porosity_features))

    if len(optimal_porosity_features) != len(BASELINE_POROSITY_FEATURES):
        improvement = "improvement" if len(optimal_porosity_features) > len(BASELINE_POROSITY_FEATURES) else "reduction"
        IJ.log("  Feature " + improvement + ": " + str(len(BASELINE_POROSITY_FEATURES)) + " -> " + str(len(optimal_porosity_features)) + " features")

    IJ.log("")
    IJ.log("SHAPE CLASSIFICATION:")
    IJ.log("  Best Model: " + best_shape['classifier_name'])
    IJ.log("  Accuracy: " + str(round(best_shape.get('accuracy', 0.0), 3)))
    IJ.log("  Weighted F1: " + str(round(best_shape.get('weighted_f1', 0.0), 3)))

    # Additional metrics if available
    if 'weighted_precision' in best_shape:
        IJ.log("  Weighted Precision: " + str(round(best_shape['weighted_precision'], 3)))
        IJ.log("  Weighted Recall: " + str(round(best_shape['weighted_recall'], 3)))

    if 'kappa' in best_shape:
        IJ.log("  Cohen's Kappa: " + str(round(best_shape['kappa'], 3)))

    if 'accuracy_std' in best_shape:
        IJ.log("  Accuracy Std Dev: " + str(round(best_shape['accuracy_std'], 3)))

    if 'statistical_significance' in best_shape:
        IJ.log("  Statistical Significance: " + best_shape['statistical_significance'])

    IJ.log("  Optimal Features (" + str(len(optimal_shape_features)) + "): " + ", ".join(optimal_shape_features))

    if len(optimal_shape_features) != len(BASELINE_SHAPE_FEATURES):
        improvement = "improvement" if len(optimal_shape_features) > len(BASELINE_SHAPE_FEATURES) else "reduction"
        IJ.log("  Feature " + improvement + ": " + str(len(BASELINE_SHAPE_FEATURES)) + " -> " + str(len(optimal_shape_features)) + " features")

    # Optimization summary
    IJ.log("")
    IJ.log("OPTIMIZATION SUMMARY:")
    if ENABLE_FEATURE_SELECTION:
        IJ.log("  Feature Selection: ENABLED")
    if ENABLE_DETAILED_OPTIMIZATION:
        IJ.log("  Detailed Optimization: ENABLED")
    if ENABLE_NESTED_CV:
        IJ.log("  Nested Cross-Validation: ENABLED")
    if ENABLE_STATISTICAL_TESTING:
        IJ.log("  Statistical Testing: ENABLED")
    if ENABLE_DETAILED_METRICS:
        IJ.log("  Detailed Metrics: ENABLED")


    str_porosity_features = "; ".join(optimal_porosity_features)
    str_shape_features = "; ".join(optimal_shape_features)

    IJ.log("")
    IJ.log("INTEGRATION NOTES:")
    IJ.log("To use these optimized configurations in your ImageJ/FIJI scripts:")
    IJ.log("1. Update POROSITY_REQUIRED_FEATURES in AC.py, copy this: \n\"" + str_porosity_features + "\"")
    IJ.log("2. Update SHAPE_REQUIRED_FEATURES in AC.py, copy this: \n\"" + str_shape_features + "\"")
    IJ.log("3. Use the saved .model files with optimal configurations")
    IJ.log("4. Have fun!")

    return True

# === SCRIPT EXECUTION ===
if __name__ == '__main__':
    try:
        success = main()
        if success:
            IJ.log("Model selection completed successfully!")
        else:
            IJ.log("Model selection failed!")
    except Exception as e:
        IJ.log("ERROR: " + str(e))
        import traceback
        traceback.print_exc()
